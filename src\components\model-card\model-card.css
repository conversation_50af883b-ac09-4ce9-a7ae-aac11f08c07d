/* 模型卡片组件样式 - 根据Figma设计稿精准还原 */
.model-card {
display: flex;
width: 200px;
height: 180px;
padding: 20px;
flex-direction: column;
align-items: center;
gap: 19px;
flex-shrink: 0;
border-radius: 12px;
border: 1px solid rgba(255, 255, 255, 0.32);
background: linear-gradient(144deg, rgba(0, 0, 0, 0.20) 0%, rgba(255, 255, 255, 0.05) 98.73%);
box-shadow: 0px 1px 3px 0px rgba(255, 255, 255, 0.25) inset;
backdrop-filter: blur(15px);
}

.model-card:hover {
  transform: translateY(-6px);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0px 1px 3px 0px rgba(255, 255, 255, 0.35) inset, 0 16px 48px rgba(0, 0, 0, 0.2);
}

/* 图片容器 */
.model-card__image-container {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}


.model-card__content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  margin-bottom: 8px;
}

.model-card__title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  color: var(--color-content-accent);
  margin: 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}




/* 响应式设计 */
@media (max-width: 768px) {
  .model-card__content {
    padding: 12px 16px 16px 16px;
  }

  .model-card__title {
    font-size: var(--font-size-base);
  }

  .model-card__overlay-text {
    font-size: var(--font-size-base);
  }
}

/* 加载状态 */
.model-card--loading {
  pointer-events: none;
  opacity: 0.6;
}

.model-card--loading .model-card__image-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 1.5s infinite;
}

/* 动画定义 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}